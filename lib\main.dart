import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:vosk_flutter/vosk_flutter.dart';
import 'package:android_intent_plus/android_intent.dart';


void main() {
  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late VoskSpeechService _speechService;
  String _recognized = 'Listening...';

  @override
  void initState() {
    super.initState();
    initVosk();
  }

  Future<void> initVosk() async {
    await Permission.microphone.request();

    final model = await VoskModel.load(
      'assets/vosk-model-small-en-us-0.15',
    );

    _speechService = VoskSpeechService(model);
    await _speechService.start();

    _speechService.onResult.listen((result) {
      if (result.text.isNotEmpty) {
        setState(() {
          _recognized = result.text;
        });

        // Detect hotword-like phrases
        if (result.text.contains('next')) {
          triggerMediaControl('next');
        } else if (result.text.contains('back') || result.text.contains('previous')) {
          triggerMediaControl('previous');
        }
      }
    });
  }

  void triggerMediaControl(String action) {
    print("🔥 Triggering: $action");
    // You can later hook Android intents here for controlling media.
    // For now, just printing to test recognition
  }

  @override
  void dispose() {
    _speechService.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: Text('Voice Command Music')),
        body: Center(child: Text(_recognized, style: TextStyle(fontSize: 24))),
      ),
    );
  }
}
